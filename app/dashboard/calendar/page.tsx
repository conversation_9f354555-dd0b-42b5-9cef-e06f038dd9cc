'use client'

import { DashboardLayout } from '@/common/components/organisms';
import { Calendar } from '@/common/components/molecules';
import { useProjects } from '@/common/hooks';
import { Link } from '@/common/components/atoms';
import { routes } from '@/common/routes';

export default function CalendarPage () {
  const { activeProject } = useProjects();
  const connectedAccounts = activeProject?.accounts || [];
  const hasConnectedAccounts = connectedAccounts.some(account =>
    account.connected && account.agentId,
  );

  return (
    <DashboardLayout>
      <div className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6 mb-6">
        <h2 className="text-xl font-semibold text-white mb-4">Content Calendar</h2>
        {hasConnectedAccounts ? (
          <Calendar connectedAccounts={connectedAccounts} />
        ) : (
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-violets-are-blue/20 p-4 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-violets-are-blue">
                  <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                  <line x1="16" x2="16" y1="2" y2="6" />
                  <line x1="8" x2="8" y1="2" y2="6" />
                  <line x1="3" x2="21" y1="10" y2="10" />
                  <path d="M8 14h.01" />
                  <path d="M12 14h.01" />
                  <path d="M16 14h.01" />
                  <path d="M8 18h.01" />
                  <path d="M12 18h.01" />
                  <path d="M16 18h.01" />
                </svg>
              </div>
            </div>
            <h3 className="text-white text-lg font-medium mb-2">No content calendar yet</h3>
            <p className="text-gray-400 mb-6 max-w-md mx-auto">Connect a social media account to start scheduling and viewing your content calendar</p>
            <div className='flex justify-center'>
              <Link href={routes.dashboardAccountsPath} variant='gradient' size='md'>
                Connect Account
              </Link>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
