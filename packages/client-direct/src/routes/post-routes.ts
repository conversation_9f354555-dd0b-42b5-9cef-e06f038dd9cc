import express from "express";
import {
    AgentRuntime,
    composeContext,
    generateText,
    ModelClass,
} from "@elizaos/core";
import { upload } from "../utils/upload-config";
import * as fs from "fs";
import * as path from "path";
import { CustomRequest } from "../models/types";
import {
    copyImprovementTemplate,
    postTemplate,
    postWithoutKnowledgeBaseTemplate,
} from "../templates/post-template";

// Import the ScheduledPost interface
interface ScheduledPost {
    id: string;
    content: string;
    scheduledTime: Date;
    status: "draft" | "approved" | "rejected" | "posted";
    topics?: string[];
    notes?: string;
    reviewFeedback?: string;
    attachments?: string[];
    localPath?: Array<{
        type: string;
        url: string;
    }>;
}

/**
 * Create router for tweet-related endpoints
 * @param agents Map of agent runtimes
 * @returns Express router
 */
export function createPostRoutes(
    agents: Map<string, AgentRuntime>
): express.Router {
    const router = express.Router();

    // Get tweets for an agent
    router.get("/agents/:agentId/posts", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }
        if (agent.clients["linkedin"]) {
            const linkedInClient = agent.clients["linkedin"];
            if (linkedInClient && linkedInClient.post.currentPlanId) {
                const postManager = linkedInClient.post;
                const contentManager = postManager.contentPlanManager;
                const plan = await contentManager.getPlan(
                    postManager.currentPlanId
                );
                plan.posts = plan.posts.sort(
                    (a: ScheduledPost, b: ScheduledPost) =>
                        new Date(a.scheduledTime).getTime() -
                        new Date(b.scheduledTime).getTime()
                );
                res.json({
                    id: agent.agentId,
                    plan: {
                        id: plan.id,
                        posts: plan.posts,
                        metadata: {
                            totalPosts: plan.posts.length,
                        },
                    },
                });
                return;
            }
        }
        if (agent.clients["twitter"]) {
            const twitterClient = agent.clients["twitter"];
            if (twitterClient && twitterClient.post.currentPlanId) {
                const postManager = twitterClient.post;
                const contentManager = postManager.contentPlanManager;
                const plan = await contentManager.getPlan(
                    postManager.currentPlanId
                );
                plan.posts = plan.posts.sort(
                    (a: ScheduledPost, b: ScheduledPost) =>
                        new Date(a.scheduledTime).getTime() -
                        new Date(b.scheduledTime).getTime()
                );
                res.json({
                    id: agent.agentId,
                    plan: {
                        id: plan.id,
                        posts: plan.posts,
                        metadata: {
                            totalPosts: plan.posts.length,
                        },
                    },
                });
                return;
            }
        }

        res.status(404).json({ error: "Could not find agent posts" });
    });

    // Update post with attachments (combined endpoint)
    router.post(
        "/agents/:agentId/update-post",
        upload.single("image"),
        async (req: CustomRequest, res: express.Response) => {
            const agentId = req.params.agentId;
            const agent = agents.get(agentId);
            const postId = req.body.postId;
            const planId = req.body.planId;
            const content = req.body.content;
            const imageFromAI = req.body.imageFromAI;
            const scheduledTime = req.body.scheduledTime;
            let filepath = imageFromAI;
            let newPath: string | undefined;

            if (!agent) {
                res.status(404).json({ error: "Agent not found" });
                return;
            }

            // Process image if provided (either uploaded or from AI)
            if (imageFromAI || req.file) {
                // Handle AI-generated image
                if (imageFromAI) {
                    const baseDir = path.join(process.cwd(), "generatedImages");
                    // Create the directory if it doesn't exist
                    if (!fs.existsSync(baseDir)) {
                        fs.mkdirSync(baseDir, { recursive: true });
                    }
                    const generatedFileName = imageFromAI?.split("/").pop();
                    newPath = path.join(baseDir, `${generatedFileName}`);
                }
                // Handle uploaded image
                else if (req.file) {
                    const uploadDir = path.dirname(req.file.path);
                    // Create the directory if it doesn't exist
                    if (!fs.existsSync(uploadDir)) {
                        fs.mkdirSync(uploadDir, { recursive: true });
                    }
                    const newFilename = `${planId}_${Date.now()}`;
                    newPath = path.join(uploadDir, newFilename);
                    newPath = newPath + "." + req.file.mimetype.split("/")[1];
                    fs.renameSync(req.file.path, newPath);
                    const normalizedPath = newPath.replace(/\\/g, "/");
                    const filename = normalizedPath.split("/").pop();
                    filepath = `https://media-pilot.dreamstarter.xyz/media/uploads/${filename}`;
                }
            }

            try {
                const twitterClient = agent.clients["twitter"];
                const linkedInClient = agent.clients["linkedin"];

                // Prepare update object with all the fields that need to be updated
                const updateObject: {
                    content?: string;
                    scheduledTime?: Date;
                    attachments?: string[];
                    localPath?: Array<{
                        type: string;
                        url: string;
                    }>;
                } = {};

                // Add content if provided
                if (content !== undefined) {
                    updateObject.content = content;
                }

                // Add scheduled time if provided
                if (scheduledTime !== undefined) {
                    updateObject.scheduledTime = new Date(scheduledTime);
                }

                // Add attachments if image was provided
                if (filepath) {
                    updateObject.attachments = [filepath];
                    if (newPath) {
                        updateObject.localPath = [
                            {
                                type: imageFromAI
                                    ? "image/png"
                                    : req.file?.mimetype,
                                url: newPath,
                            },
                        ];
                    }
                }

                // If no image was provided but we're explicitly updating attachments (e.g., removing an image)
                if (filepath === null) {
                    updateObject.attachments = [];
                    updateObject.localPath = [];
                }

                // Update the post with all the provided fields
                if (linkedInClient && linkedInClient.post.currentPlanId) {
                    const postManager = linkedInClient.post;
                    const contentManager = postManager.contentPlanManager;
                    await contentManager.updatePost(
                        planId,
                        postId,
                        updateObject
                    );
                    res.json({
                        status: "success",
                    });
                    return;
                }

                if (twitterClient && twitterClient.post.currentPlanId) {
                    const postManager = twitterClient.post;
                    const contentManager = postManager.contentPlanManager;
                    await contentManager.updatePost(
                        planId,
                        postId,
                        updateObject
                    );
                    res.json({
                        status: "success",
                    });
                    return;
                }

                res.status(404).json({
                    status: "failed",
                    error: "No valid social media client found",
                });
            } catch (error) {
                console.error("Error updating post:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Error updating post",
                });
            }
        }
    );

    // Generate new tweets
    router.post("/agents/:agentId/new-tweets", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        const planId = req.body.planId;
        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }

        const twitterClient = agent.clients["twitter"];
        const linkedInClient = agent.clients["linkedin"];
        if (linkedInClient && linkedInClient.post.currentPlanId) {
            const postManager = linkedInClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                await contentManager.refreshNewPosts(
                    planId,
                    postManager.postInterval
                );

                res.json({
                    status: "success",
                    message: "New Posts generated successfully",
                });
            } catch (error) {
                console.error("Error generating posts:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Error generating posts",
                });
            }
            return;
        }
        if (twitterClient && twitterClient.post.currentPlanId) {
            const postManager = twitterClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                await contentManager.refreshNewPosts(
                    planId,
                    postManager.postInterval
                );

                res.json({
                    status: "success",
                    message: "New Posts generated successfully",
                });
            } catch (error) {
                console.error("Error generating posts:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Error generating posts",
                });
            }
            return;
        }

        res.status(404).json({
            status: "failed",
            error: "Twitter client or plan not found",
        });
    });

    // Remove tweet image
    router.delete("/agents/:agentId/remove-tweet-image", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        const postId = req.body.postId;
        const planId = req.body.planId;

        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }

        const twitterClient = agent.clients["twitter"];
        const linkedInClient = agent.clients["linkedin"];
        if (linkedInClient && linkedInClient.post.currentPlanId) {
            const postManager = linkedInClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                // Update the post with empty attachments array to remove images
                await contentManager.updatePost(planId, postId, {
                    attachments: [],
                });

                res.json({
                    status: "success",
                    message: "Image removed successfully",
                });
            } catch (error) {
                console.error("Error removing image:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Failed to remove image",
                });
            }
            return;
        }
        if (twitterClient && twitterClient.post.currentPlanId) {
            const postManager = twitterClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                // Update the post with empty attachments array to remove images
                await contentManager.updatePost(planId, postId, {
                    attachments: [],
                });

                res.json({
                    status: "success",
                    message: "Image removed successfully",
                });
            } catch (error) {
                console.error("Error removing image:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Failed to remove image",
                });
            }
            return;
        }

        res.status(404).json({
            status: "failed",
            error: "Twitter client or plan not found",
        });
    });

    // Delete a scheduled post
    router.delete("/agents/:agentId/delete-post", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        const postId = req.body.postId;
        const planId = req.body.planId;

        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }

        if (!postId || !planId) {
            res.status(400).json({ error: "Post ID and Plan ID are required" });
            return;
        }

        const twitterClient = agent.clients["twitter"];
        const linkedInClient = agent.clients["linkedin"];

        if (linkedInClient && linkedInClient.post.currentPlanId) {
            const postManager = linkedInClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                // Get the current plan
                const plan = await contentManager.getPlan(planId);
                if (!plan) {
                    res.status(404).json({
                        status: "failed",
                        error: "Content plan not found",
                    });
                    return;
                }

                // Find the post index
                const postIndex = plan.posts.findIndex(
                    (post: ScheduledPost) => post.id === postId
                );
                if (postIndex === -1) {
                    res.status(404).json({
                        status: "failed",
                        error: "Post not found in the plan",
                    });
                    return;
                }

                // Remove the post from the array
                plan.posts.splice(postIndex, 1);

                // Update the metadata
                plan.metadata.totalPosts = plan.posts.length;

                // Save the updated plan
                await contentManager.storePlan(plan);

                res.json({
                    status: "success",
                    message: "Post deleted successfully",
                });
                return;
            } catch (error) {
                console.error("Error deleting post:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Failed to delete post",
                });
                return;
            }
        }

        if (twitterClient && twitterClient.post.currentPlanId) {
            const postManager = twitterClient.post;
            const contentManager = postManager.contentPlanManager;

            try {
                // Get the current plan
                const plan = await contentManager.getPlan(planId);
                if (!plan) {
                    res.status(404).json({
                        status: "failed",
                        error: "Content plan not found",
                    });
                    return;
                }

                // Find the post index
                const postIndex = plan.posts.findIndex(
                    (post: ScheduledPost) => post.id === postId
                );
                if (postIndex === -1) {
                    res.status(404).json({
                        status: "failed",
                        error: "Post not found in the plan",
                    });
                    return;
                }

                // Remove the post from the array
                plan.posts.splice(postIndex, 1);

                // Update the metadata
                plan.metadata.totalPosts = plan.posts.length;

                // Save the updated plan
                await contentManager.storePlan(plan);

                res.json({
                    status: "success",
                    message: "Post deleted successfully",
                });
                return;
            } catch (error) {
                console.error("Error deleting post:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Failed to delete post",
                });
                return;
            }
        }

        res.status(404).json({
            status: "failed",
            error: "No valid social media client found",
        });
    });

    // Schedule a post
    router.post(
        "/agents/:agentId/schedule-post",
        upload.single("image"),
        async (req: CustomRequest, res: express.Response) => {
            const agentId = req.params.agentId;
            const agent = agents.get(agentId);
            const { content, scheduledTime, platform, imageFromAI } = req.body;
            let filepath = imageFromAI;
            let newPath: string | undefined;

            if (!agent) {
                res.status(404).json({ error: "Agent not found" });
                return;
            }

            // Process image if provided (either uploaded or from AI)
            if (imageFromAI || req.file) {
                // Handle AI-generated image
                if (imageFromAI) {
                    const baseDir = path.join(process.cwd(), "generatedImages");
                    // Create the directory if it doesn't exist
                    if (!fs.existsSync(baseDir)) {
                        fs.mkdirSync(baseDir, { recursive: true });
                    }
                    const generatedFileName = imageFromAI?.split("/").pop();
                    newPath = path.join(baseDir, `${generatedFileName}`);
                }
                // Handle uploaded image
                else if (req.file) {
                    const uploadDir = path.dirname(req.file.path);
                    // Create the directory if it doesn't exist
                    if (!fs.existsSync(uploadDir)) {
                        fs.mkdirSync(uploadDir, { recursive: true });
                    }
                    const newFilename = `schedule_${Date.now()}`;
                    newPath = path.join(uploadDir, newFilename);
                    newPath = newPath + "." + req.file.mimetype.split("/")[1];
                    fs.renameSync(req.file.path, newPath);
                    const normalizedPath = newPath.replace(/\\/g, "/");
                    const filename = normalizedPath.split("/").pop();
                    filepath = `https://media-pilot.dreamstarter.xyz/media/uploads/${filename}`;
                }
            }

            try {
                const results = [];
                // Handle LinkedIn platform
                if (platform === "linkedin" && agent.clients["linkedin"]) {
                    const linkedInClient = agent.clients["linkedin"];
                    if (linkedInClient && linkedInClient.post.currentPlanId) {
                        const postManager = linkedInClient.post;
                        const contentManager = postManager.contentPlanManager;

                        // Create new post object with optional attachments
                        const newPost: ScheduledPost = {
                            id: `post-${Date.now()}-${Math.random().toString(36).substring(7)}`,
                            content: content,
                            scheduledTime: new Date(scheduledTime),
                            status: "approved",
                            topics: [], // You might want to extract topics from content
                        };

                        // Add attachment if image was provided
                        if (filepath) {
                            newPost.attachments = [filepath];
                            if (newPath) {
                                newPost.localPath = [
                                    {
                                        type: imageFromAI
                                            ? "image/png"
                                            : req.file?.mimetype,
                                        url: newPath,
                                    },
                                ];
                            }
                        }

                        const plan = await contentManager.getPlan(
                            postManager.currentPlanId
                        );
                        if (!plan) {
                            results.push({
                                platform,
                                status: "failed",
                                error: "Content plan not found",
                            });
                        }

                        plan.posts.push(newPost);
                        plan.metadata.totalPosts = plan.posts.length;

                        plan.posts = plan.posts.sort(
                            (a: ScheduledPost, b: ScheduledPost) =>
                                new Date(a.scheduledTime).getTime() -
                                new Date(b.scheduledTime).getTime()
                        );

                        await contentManager.storePlan(plan);

                        results.push({
                            platform,
                            status: "success",
                            post: newPost,
                        });
                    }
                }
                // Handle Twitter platform
                else if (platform === "twitter" && agent.clients["twitter"]) {
                    const twitterClient = agent.clients["twitter"];
                    if (twitterClient && twitterClient.post.currentPlanId) {
                        const postManager = twitterClient.post;
                        const contentManager = postManager.contentPlanManager;

                        // Create new post object with optional attachments
                        const newPost: ScheduledPost = {
                            id: `post-${Date.now()}-${Math.random().toString(36).substring(7)}`,
                            content: content,
                            scheduledTime: new Date(scheduledTime),
                            status: "approved",
                            topics: [], // You might want to extract topics from content
                        };

                        // Add attachment if image was provided
                        if (filepath) {
                            newPost.attachments = [filepath];
                            if (newPath) {
                                newPost.localPath = [
                                    {
                                        type: imageFromAI
                                            ? "image/png"
                                            : req.file?.mimetype,
                                        url: newPath,
                                    },
                                ];
                            }
                        }

                        const plan = await contentManager.getPlan(
                            postManager.currentPlanId
                        );
                        if (!plan) {
                            results.push({
                                platform,
                                status: "failed",
                                error: "Content plan not found",
                            });
                        }

                        plan.posts.push(newPost);
                        plan.metadata.totalPosts = plan.posts.length;

                        plan.posts = plan.posts.sort(
                            (a: ScheduledPost, b: ScheduledPost) =>
                                new Date(a.scheduledTime).getTime() -
                                new Date(b.scheduledTime).getTime()
                        );

                        await contentManager.storePlan(plan);

                        results.push({
                            platform,
                            status: "success",
                            post: newPost,
                        });
                    }
                }
                if (results.length === 0) {
                    res.status(400).json({
                        status: "failed",
                        error: "No valid platforms found or missing client configurations",
                    });
                    return;
                }
                res.json({
                    status: "success",
                    results,
                });
            } catch (error) {
                console.error("Error scheduling posts:", error);
                res.status(500).json({
                    status: "failed",
                    error: "Error scheduling posts",
                });
            }
        }
    );

    // Improve a post using a prompt
    router.post("/agents/:agentId/improve-post-content", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        const { systemPrompt, context } = req.body;

        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }

        if (!systemPrompt) {
            res.status(400).json({ error: "System prompt is required" });
            return;
        }

        // Default context if not provided
        const postContext = context || "";

        // Determine model size (SMALL, MEDIUM, LARGE)
        const modelClass = ModelClass.LARGE;

        try {
            // Generate post content using the provided system prompt
            const postContent = await generateText({
                runtime: agent,
                context: copyImprovementTemplate.replace(
                    "%originalCopy%",
                    postContext
                ),
                modelClass: modelClass,
                customSystemPrompt: systemPrompt,
            });

            // Clean up the response to ensure it's properly formatted
            const cleanedContent = postContent
                .trim()
                .replace(/^```json\s*|\s*```$/g, "") // Remove markdown code blocks if present
                .replace(/^['"](.*)['"]$/g, "$1") // Remove outer quotes if present
                .replace(/\\n/g, "\n") // Replace escaped newlines
                .replace(/\\"/g, '"'); // Replace escaped quotes

            res.json({
                status: "success",
                post: {
                    content: cleanedContent,
                },
            });
        } catch (error) {
            console.error("Error generating post:", error);
            res.status(500).json({
                status: "failed",
                error: "Error generating post",
            });
        }
    });

    // Generate Post
    router.post("/agents/:agentId/generate-post-content", async (req, res) => {
        const agentId = req.params.agentId;
        const agent = agents.get(agentId);
        const { systemPrompt, isKnowledgeBase } = req.body;

        if (!agent) {
            res.status(404).json({ error: "Agent not found" });
            return;
        }

        if (!systemPrompt) {
            res.status(400).json({ error: "System prompt is required" });
            return;
        }

        const modelClass = ModelClass.LARGE;

        try {
            const state = await agent.composeState({
                userId: agent.agentId,
                roomId: agent.agentId,
                agentId: agent.agentId,
                content: {
                    text: systemPrompt,
                },
            });

            const context = composeContext({
                state,
                template: isKnowledgeBase
                    ? postTemplate
                    : postWithoutKnowledgeBaseTemplate,
            });
            // Generate post content using the provided system prompt
            const postContent = await generateText({
                runtime: agent,
                context,
                modelClass: modelClass,
                customSystemPrompt: `Generate a linkedin post on this topic: ${systemPrompt}`,
            });

            // Clean up the response to ensure it's properly formatted
            const cleanedContent = postContent
                .trim()
                .replace(/^```json\s*|\s*```$/g, "") // Remove markdown code blocks if present
                .replace(/^['"](.*)['"]$/g, "$1") // Remove outer quotes if present
                .replace(/\\n/g, "\n") // Replace escaped newlines
                .replace(/\\"/g, '"'); // Replace escaped quotes

            res.json({
                status: "success",
                post: {
                    content: cleanedContent,
                },
            });
        } catch (error) {
            console.error("Error generating post:", error);
            res.status(500).json({
                status: "failed",
                error: "Error generating post",
            });
        }
    });

    return router;
}
